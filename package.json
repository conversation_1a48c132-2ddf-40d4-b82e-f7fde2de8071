{"name": "ai-coder-with-auth", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "node server/index.js", "start": "node server/index.js", "server": "node server/index.js", "build": "echo 'Build completed for Vercel deployment'", "vercel-build": "echo 'Vercel build completed'", "test:deployment": "node test-vercel-deployment.js", "test:vercel": "TEST_URL=https://your-app.vercel.app node test-vercel-deployment.js", "migrate": "node scripts/migrate-to-postgres.js", "migrate:dry-run": "node scripts/migrate-to-postgres.js --dry-run", "migrate:backup": "node scripts/migrate-to-postgres.js --backup"}, "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "firebase-admin": "^12.0.0", "uuid": "^9.0.1", "dotenv": "^16.3.1", "pg": "^8.11.3", "connect-pg-simple": "^9.0.1", "@google/generative-ai": "^0.24.1"}, "devDependencies": {"@types/node": "^20.0.0", "node-fetch": "^3.3.2"}}