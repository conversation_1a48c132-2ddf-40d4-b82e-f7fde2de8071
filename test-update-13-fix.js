// Test script to verify the "update 13" SSE connection fix
import fetch from 'node-fetch';

const DEPLOYMENT_URL = 'https://ai-coder-agentic-kod2x688c-suhailult777s-projects.vercel.app';

async function testSSEEndpoint() {
    console.log('🧪 Testing SSE Endpoint Fix for "Update 13" Issue\n');
    
    try {
        console.log('📡 Testing SSE endpoint: /api/agent-stream');
        
        const response = await fetch(`${DEPLOYMENT_URL}/api/agent-stream`, {
            method: 'GET',
            headers: {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
        });
        
        console.log(`📊 Response Status: ${response.status}`);
        console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
        
        if (response.ok && response.headers.get('content-type')?.includes('text/event-stream')) {
            console.log('✅ SSE endpoint is accessible and returning correct headers');
            
            // Read a few chunks of the stream to verify it's working
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let chunks = 0;
            const maxChunks = 3;
            
            console.log('\n📡 Reading SSE stream data:');
            
            while (chunks < maxChunks) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                console.log(`   Chunk ${chunks + 1}: ${chunk.substring(0, 100)}...`);
                chunks++;
            }
            
            reader.releaseLock();
            console.log('\n✅ SSE streaming is working correctly!');
            console.log('🎉 The "update 13" issue has been FIXED!');
            
            return true;
        } else {
            console.log('❌ SSE endpoint failed');
            console.log(`   Status: ${response.status}`);
            console.log(`   Content-Type: ${response.headers.get('content-type')}`);
            return false;
        }
        
    } catch (error) {
        console.log('💥 Test Error:', error.message);
        return false;
    }
}

async function testOldEndpoint() {
    console.log('\n🔍 Testing old endpoint to confirm it fails:');
    
    try {
        const response = await fetch(`${DEPLOYMENT_URL}/api/agent/status/stream`, {
            method: 'GET'
        });
        
        console.log(`📊 Old endpoint status: ${response.status}`);
        
        if (response.status === 404) {
            console.log('✅ Old endpoint correctly returns 404 (as expected)');
            return true;
        } else {
            console.log('⚠️ Old endpoint unexpectedly accessible');
            return false;
        }
        
    } catch (error) {
        console.log('✅ Old endpoint correctly fails (as expected)');
        return true;
    }
}

async function testAgentEndpoint() {
    console.log('\n🤖 Testing agent endpoint functionality:');
    
    try {
        const response = await fetch(`${DEPLOYMENT_URL}/api/agent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: 'Test agent functionality'
            })
        });
        
        console.log(`📊 Agent endpoint status: ${response.status}`);
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Agent endpoint is working');
            console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`);
            return true;
        } else {
            console.log('❌ Agent endpoint failed');
            return false;
        }
        
    } catch (error) {
        console.log('💥 Agent endpoint error:', error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Starting comprehensive test suite for "Update 13" fix\n');
    console.log('=' * 60);
    
    const results = {
        sseEndpoint: await testSSEEndpoint(),
        oldEndpoint: await testOldEndpoint(),
        agentEndpoint: await testAgentEndpoint()
    };
    
    console.log('\n' + '=' * 60);
    console.log('📋 TEST RESULTS SUMMARY:');
    console.log('=' * 60);
    
    console.log(`🔌 SSE Endpoint (/api/agent-stream): ${results.sseEndpoint ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🗑️  Old Endpoint (/api/agent/status/stream): ${results.oldEndpoint ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`🤖 Agent Endpoint (/api/agent): ${results.agentEndpoint ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(result => result);
    
    console.log('\n' + '=' * 60);
    if (allPassed) {
        console.log('🎉 ALL TESTS PASSED! The "Update 13" issue is FIXED!');
        console.log('✅ SSE connection should now work properly in the browser');
        console.log('✅ Agent mode functionality is preserved');
        console.log('✅ Real-time streaming is working');
    } else {
        console.log('❌ Some tests failed. Please check the issues above.');
    }
    console.log('=' * 60);
    
    return allPassed;
}

// Run the tests
runAllTests().catch(console.error);
