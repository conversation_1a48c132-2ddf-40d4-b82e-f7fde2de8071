// PostgreSQL storage for Vercel serverless deployment
import database from './database.js';

export class PostgreSQLUserStorage {
  constructor() {
    this.db = database;
  }

  async createUser(userData) {
    try {
      const query = `
        INSERT INTO users (email, password, name, provider)
        VALUES ($1, $2, $3, $4)
        RETURNING id, email, name, provider, created_at, updated_at
      `;
      
      const values = [
        userData.email,
        userData.password || null,
        userData.name || null,
        userData.provider || 'local'
      ];

      const result = await this.db.query(query, values);
      
      if (result.rows.length === 0) {
        throw new Error('Failed to create user');
      }

      const user = result.rows[0];
      console.log(`✅ User created: ${user.email} (${user.provider})`);
      
      return {
        id: user.id,
        email: user.email,
        password: userData.password, // Include for auth flow
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      console.error('❌ Error creating user:', error.message);
      throw error;
    }
  }

  async getUserByEmail(email) {
    try {
      const query = 'SELECT * FROM users WHERE email = $1';
      const result = await this.db.query(query, [email]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      return {
        id: user.id,
        email: user.email,
        password: user.password,
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      console.error('❌ Error getting user by email:', error.message);
      throw error;
    }
  }

  async getUserById(id) {
    try {
      const query = 'SELECT * FROM users WHERE id = $1';
      const result = await this.db.query(query, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      return {
        id: user.id,
        email: user.email,
        password: user.password,
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      console.error('❌ Error getting user by ID:', error.message);
      throw error;
    }
  }

  async updateUser(id, updateData) {
    try {
      const fields = [];
      const values = [];
      let paramCount = 1;

      // Build dynamic update query
      Object.keys(updateData).forEach(key => {
        if (key !== 'id' && key !== 'created_at') {
          fields.push(`${key} = $${paramCount}`);
          values.push(updateData[key]);
          paramCount++;
        }
      });

      if (fields.length === 0) {
        throw new Error('No valid fields to update');
      }

      const query = `
        UPDATE users 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = $${paramCount}
        RETURNING id, email, name, provider, created_at, updated_at
      `;
      
      values.push(id);
      const result = await this.db.query(query, values);
      
      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = result.rows[0];
      console.log(`✅ User updated: ${user.email}`);
      
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };
    } catch (error) {
      console.error('❌ Error updating user:', error.message);
      throw error;
    }
  }

  async deleteUser(id) {
    try {
      const query = 'DELETE FROM users WHERE id = $1 RETURNING id';
      const result = await this.db.query(query, [id]);
      
      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      console.log(`✅ User deleted: ${id}`);
      return true;
    } catch (error) {
      console.error('❌ Error deleting user:', error.message);
      throw error;
    }
  }

  async getAllUsers() {
    try {
      const query = `
        SELECT id, email, name, provider, created_at, updated_at 
        FROM users 
        ORDER BY created_at DESC
      `;
      const result = await this.db.query(query);
      
      return result.rows.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }));
    } catch (error) {
      console.error('❌ Error getting all users:', error.message);
      throw error;
    }
  }

  async getUserCount() {
    try {
      const query = 'SELECT COUNT(*) as count FROM users';
      const result = await this.db.query(query);
      return parseInt(result.rows[0].count);
    } catch (error) {
      console.error('❌ Error getting user count:', error.message);
      throw error;
    }
  }

  async getUserStats() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN provider = 'local' THEN 1 END) as local_users,
          COUNT(CASE WHEN provider = 'google' THEN 1 END) as google_users,
          COUNT(CASE WHEN provider = 'firebase-google' THEN 1 END) as firebase_google_users,
          COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as users_last_week,
          COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as users_last_month
        FROM users
      `;
      
      const result = await this.db.query(query);
      const stats = result.rows[0];
      
      return {
        total_users: parseInt(stats.total_users),
        local_users: parseInt(stats.local_users),
        google_users: parseInt(stats.google_users),
        firebase_google_users: parseInt(stats.firebase_google_users),
        users_last_week: parseInt(stats.users_last_week),
        users_last_month: parseInt(stats.users_last_month)
      };
    } catch (error) {
      console.error('❌ Error getting user stats:', error.message);
      throw error;
    }
  }

  async searchUsers(searchTerm, limit = 10, offset = 0) {
    try {
      const query = `
        SELECT id, email, name, provider, created_at, updated_at 
        FROM users 
        WHERE email ILIKE $1 OR name ILIKE $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const searchPattern = `%${searchTerm}%`;
      const result = await this.db.query(query, [searchPattern, limit, offset]);
      
      return result.rows.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }));
    } catch (error) {
      console.error('❌ Error searching users:', error.message);
      throw error;
    }
  }

  async getUsersByProvider(provider, limit = 10, offset = 0) {
    try {
      const query = `
        SELECT id, email, name, provider, created_at, updated_at 
        FROM users 
        WHERE provider = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const result = await this.db.query(query, [provider, limit, offset]);
      
      return result.rows.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        provider: user.provider,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }));
    } catch (error) {
      console.error('❌ Error getting users by provider:', error.message);
      throw error;
    }
  }

  // Health check method
  async healthCheck() {
    try {
      const dbHealth = await this.db.healthCheck();
      const userCount = await this.getUserCount();
      
      return {
        status: 'healthy',
        database: dbHealth,
        userCount: userCount,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}
