<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Debug Tool - Update 13 Issue Analysis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.info { color: #17a2b8; }
        .log-entry.warning { color: #ffc107; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .metrics {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .metric {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            min-width: 100px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SSE Debug Tool - "Update 13" Issue Analysis</h1>
        <p>Comprehensive diagnostic tool to identify and fix SSE connection issues.</p>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="connection-count">0</div>
                <div class="metric-label">Connections</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="message-count">0</div>
                <div class="metric-label">Messages</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="error-count">0</div>
                <div class="metric-label">Errors</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="uptime">0s</div>
                <div class="metric-label">Uptime</div>
            </div>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h3>🔌 SSE Connection Tests</h3>
            <div id="connection-status" class="status info">Ready to test</div>
            <button onclick="testNewEndpoint()">Test /api/agent-stream</button>
            <button onclick="testOldEndpoint()">Test /api/agent/status/stream</button>
            <button onclick="testMultipleConnections()">Test Multiple Connections</button>
            <button onclick="stopAllConnections()" id="stopBtn">Stop All</button>
            <div id="connection-log" class="log"></div>
        </div>

        <div class="container">
            <h3>📊 Message Analysis</h3>
            <div id="message-status" class="status info">No messages yet</div>
            <button onclick="analyzeMessages()">Analyze Message Pattern</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="message-log" class="log"></div>
        </div>
    </div>

    <div class="container">
        <h3>🧪 Diagnostic Results</h3>
        <div id="diagnostic-results">
            <div id="endpoint-test" class="status info">Endpoint test: Not started</div>
            <div id="connection-test" class="status info">Connection test: Not started</div>
            <div id="streaming-test" class="status info">Streaming test: Not started</div>
            <div id="timeout-test" class="status info">Timeout test: Not started</div>
            <div id="reconnection-test" class="status info">Reconnection test: Not started</div>
        </div>
    </div>

    <script>
        let eventSources = [];
        let messageCount = 0;
        let errorCount = 0;
        let connectionCount = 0;
        let startTime = Date.now();
        let messages = [];

        function updateMetrics() {
            document.getElementById('connection-count').textContent = connectionCount;
            document.getElementById('message-count').textContent = messageCount;
            document.getElementById('error-count').textContent = errorCount;
            document.getElementById('uptime').textContent = Math.floor((Date.now() - startTime) / 1000) + 's';
        }

        function addLog(message, type = 'info', logId = 'connection-log') {
            const log = document.getElementById(logId);
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            if (type === 'error') errorCount++;
            updateMetrics();
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `status ${type}`;
            }
        }

        function testNewEndpoint() {
            addLog('🧪 Testing NEW endpoint: /api/agent-stream', 'info');
            updateStatus('endpoint-test', 'Testing new endpoint...', 'info');
            
            const eventSource = new EventSource('/api/agent-stream');
            eventSources.push(eventSource);
            connectionCount++;
            
            eventSource.onopen = function(event) {
                addLog('✅ NEW endpoint connection opened successfully!', 'success');
                updateStatus('connection-test', 'New endpoint connection: ✅ PASS', 'success');
                updateStatus('endpoint-test', 'New endpoint accessible: ✅ PASS', 'success');
            };

            eventSource.onmessage = function(event) {
                messageCount++;
                messages.push({
                    timestamp: Date.now(),
                    data: event.data,
                    endpoint: 'new'
                });
                
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📨 NEW endpoint message: ${data.message || data.type}`, 'info');
                    
                    if (data.type === 'status' && data.message.includes('update')) {
                        const updateMatch = data.message.match(/update (\d+)/);
                        if (updateMatch) {
                            const updateNumber = updateMatch[1];
                            addLog(`🔄 Received update ${updateNumber} from NEW endpoint`, 'success');
                            
                            if (messageCount >= 3) {
                                updateStatus('streaming-test', 'NEW endpoint streaming: ✅ PASS', 'success');
                            }
                        }
                    }
                    
                    if (data.type === 'timeout') {
                        addLog('⏰ NEW endpoint timeout (normal for Vercel)', 'warning');
                        updateStatus('timeout-test', 'Timeout handling: ✅ PASS', 'success');
                    }
                    
                } catch (error) {
                    addLog(`❌ Error parsing NEW endpoint message: ${error.message}`, 'error');
                }
            };

            eventSource.onerror = function(event) {
                addLog('❌ NEW endpoint connection error', 'error');
                
                if (eventSource.readyState === EventSource.CLOSED) {
                    addLog('🔌 NEW endpoint connection closed', 'warning');
                } else if (eventSource.readyState === EventSource.CONNECTING) {
                    addLog('🔄 NEW endpoint attempting to reconnect...', 'info');
                    updateStatus('reconnection-test', 'Reconnection working: ✅ PASS', 'success');
                }
            };
        }

        function testOldEndpoint() {
            addLog('🧪 Testing OLD endpoint: /api/agent/status/stream', 'info');
            
            fetch('/api/agent/status/stream')
                .then(response => {
                    if (response.status === 404) {
                        addLog('✅ OLD endpoint correctly returns 404 (as expected)', 'success');
                        updateStatus('endpoint-test', 'Old endpoint correctly disabled: ✅ PASS', 'success');
                    } else {
                        addLog(`⚠️ OLD endpoint unexpectedly accessible (status: ${response.status})`, 'warning');
                        updateStatus('endpoint-test', 'Old endpoint issue: ⚠️ WARNING', 'warning');
                    }
                })
                .catch(error => {
                    addLog('✅ OLD endpoint correctly fails (as expected)', 'success');
                    updateStatus('endpoint-test', 'Old endpoint correctly disabled: ✅ PASS', 'success');
                });
        }

        function testMultipleConnections() {
            addLog('🧪 Testing multiple simultaneous connections...', 'info');
            
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    const eventSource = new EventSource('/api/agent-stream');
                    eventSources.push(eventSource);
                    connectionCount++;
                    
                    eventSource.onopen = function() {
                        addLog(`✅ Connection ${i + 1} opened`, 'success');
                    };
                    
                    eventSource.onmessage = function(event) {
                        messageCount++;
                        addLog(`📨 Connection ${i + 1} received message`, 'info');
                    };
                    
                    eventSource.onerror = function() {
                        addLog(`❌ Connection ${i + 1} error`, 'error');
                    };
                }, i * 1000);
            }
        }

        function stopAllConnections() {
            addLog('🛑 Stopping all connections...', 'info');
            
            eventSources.forEach((eventSource, index) => {
                if (eventSource && eventSource.readyState !== EventSource.CLOSED) {
                    eventSource.close();
                    addLog(`🔌 Connection ${index + 1} closed`, 'info');
                }
            });
            
            eventSources = [];
            connectionCount = 0;
            updateStatus('connection-status', 'All connections stopped', 'info');
        }

        function analyzeMessages() {
            addLog('🔍 Analyzing message patterns...', 'info', 'message-log');
            
            if (messages.length === 0) {
                addLog('❌ No messages to analyze', 'error', 'message-log');
                updateStatus('message-status', 'No messages received', 'error');
                return;
            }
            
            // Analyze message frequency
            const timeSpan = (messages[messages.length - 1].timestamp - messages[0].timestamp) / 1000;
            const frequency = messages.length / timeSpan;
            
            addLog(`📊 Total messages: ${messages.length}`, 'info', 'message-log');
            addLog(`⏱️ Time span: ${timeSpan.toFixed(1)}s`, 'info', 'message-log');
            addLog(`📈 Frequency: ${frequency.toFixed(2)} msg/s`, 'info', 'message-log');
            
            // Check for update pattern
            const updateMessages = messages.filter(msg => {
                try {
                    const data = JSON.parse(msg.data);
                    return data.message && data.message.includes('update');
                } catch (e) {
                    return false;
                }
            });
            
            addLog(`🔄 Update messages: ${updateMessages.length}`, 'info', 'message-log');
            
            if (updateMessages.length > 0) {
                const updateNumbers = updateMessages.map(msg => {
                    const data = JSON.parse(msg.data);
                    const match = data.message.match(/update (\d+)/);
                    return match ? parseInt(match[1]) : 0;
                });
                
                addLog(`📊 Update sequence: ${updateNumbers.join(', ')}`, 'info', 'message-log');
                
                // Check if sequence resets
                const hasReset = updateNumbers.some((num, index) => 
                    index > 0 && num < updateNumbers[index - 1]
                );
                
                if (hasReset) {
                    addLog('⚠️ ISSUE FOUND: Update counter resets detected!', 'error', 'message-log');
                    updateStatus('message-status', 'Counter reset issue detected', 'error');
                } else {
                    addLog('✅ Update sequence is continuous', 'success', 'message-log');
                    updateStatus('message-status', 'Message pattern normal', 'success');
                }
            }
        }

        function clearLogs() {
            document.getElementById('connection-log').innerHTML = '';
            document.getElementById('message-log').innerHTML = '';
            messages = [];
            messageCount = 0;
            errorCount = 0;
            startTime = Date.now();
            updateMetrics();
        }

        // Auto-start basic test
        window.addEventListener('load', function() {
            addLog('🌐 Debug tool loaded, starting basic diagnostics...', 'info');
            setTimeout(testOldEndpoint, 1000);
            setTimeout(testNewEndpoint, 2000);
            
            // Update metrics every second
            setInterval(updateMetrics, 1000);
        });
    </script>
</body>
</html>
