// Test script to verify connection fixes for serverless deployment
import fetch from 'node-fetch';

const DEPLOYMENT_URL = process.env.TEST_URL || 'http://localhost:3000';

console.log('🧪 Testing Connection Fixes for Serverless Deployment');
console.log(`📍 Testing against: ${DEPLOYMENT_URL}`);

// Test 1: Health Check
async function testHealthCheck() {
    console.log('\n1️⃣ Testing Health Check...');
    try {
        const response = await fetch(`${DEPLOYMENT_URL}/api/health`);
        const data = await response.json();

        if (response.ok || response.status === 503) {
            // Accept both OK and DEGRADED status as valid
            const status = data.status === 'DEGRADED' ? '⚠️ Health check degraded (expected)' : '✅ Health check passed';
            console.log(status);
            console.log(`   Status: ${data.status}`);
            console.log(`   Environment: ${data.environment}`);
            console.log(`   SSE Connections: ${data.sse_connections?.active || 0}`);
            console.log(`   Agent Mode: ${data.agent_mode?.status || 'unknown'}`);
            console.log(`   Database: ${data.database?.status || 'unknown'}`);
            return true;
        } else {
            console.log('❌ Health check failed:', data);
            return false;
        }
    } catch (error) {
        console.log('❌ Health check error:', error.message);
        return false;
    }
}

// Test 2: SSE Connection
async function testSSEConnection() {
    console.log('\n2️⃣ Testing SSE Connection...');
    return new Promise((resolve) => {
        const timeout = setTimeout(() => {
            console.log('❌ SSE connection timeout');
            resolve(false);
        }, 10000);

        try {
            fetch(`${DEPLOYMENT_URL}/api/agent-stream`, {
                method: 'GET',
                headers: {
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                }
            }).then(response => {
                if (!response.ok) {
                    clearTimeout(timeout);
                    console.log('❌ SSE connection failed:', response.status);
                    resolve(false);
                    return;
                }

                console.log('✅ SSE connection established');
                console.log(`   Status: ${response.status}`);
                console.log(`   Content-Type: ${response.headers.get('content-type')}`);

                clearTimeout(timeout);
                resolve(true);
            }).catch(error => {
                clearTimeout(timeout);
                console.log('❌ SSE connection error:', error.message);
                resolve(false);
            });
        } catch (error) {
            clearTimeout(timeout);
            console.log('❌ SSE connection error:', error.message);
            resolve(false);
        }
    });
}

// Test 3: Authentication Endpoints
async function testAuthEndpoints() {
    console.log('\n3️⃣ Testing Authentication Endpoints...');

    // Test login endpoint
    try {
        const response = await fetch(`${DEPLOYMENT_URL}/api/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'wrongpassword'
            })
        });

        if (response.status === 401) {
            console.log('✅ Login endpoint working (correctly rejected invalid credentials)');
            return true;
        } else {
            console.log('⚠️ Login endpoint responded unexpectedly:', response.status);
            const data = await response.json();
            console.log('   Response:', data);
            return false;
        }
    } catch (error) {
        console.log('❌ Login endpoint error:', error.message);
        return false;
    }
}

// Test 4: Agent Endpoint
async function testAgentEndpoint() {
    console.log('\n4️⃣ Testing Agent Endpoint...');

    try {
        const response = await fetch(`${DEPLOYMENT_URL}/api/agent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: 'Create a simple hello world HTML page'
            })
        });

        const data = await response.json();

        if (response.ok) {
            console.log('✅ Agent endpoint working');
            console.log(`   Status: ${data.status}`);
            console.log(`   Message: ${data.message}`);
            return true;
        } else {
            console.log('❌ Agent endpoint failed:', data);
            return false;
        }
    } catch (error) {
        console.log('❌ Agent endpoint error:', error.message);
        return false;
    }
}

// Test 5: CORS Headers
async function testCORSHeaders() {
    console.log('\n5️⃣ Testing CORS Headers...');

    try {
        const response = await fetch(`${DEPLOYMENT_URL}/api/agent-stream`, {
            method: 'OPTIONS'
        });

        const corsOrigin = response.headers.get('access-control-allow-origin');
        const corsMethods = response.headers.get('access-control-allow-methods');
        const corsCredentials = response.headers.get('access-control-allow-credentials');

        if (corsOrigin && corsMethods) {
            console.log('✅ CORS headers present');
            console.log(`   Origin: ${corsOrigin}`);
            console.log(`   Methods: ${corsMethods}`);
            console.log(`   Credentials: ${corsCredentials}`);
            return true;
        } else {
            console.log('❌ CORS headers missing');
            return false;
        }
    } catch (error) {
        console.log('❌ CORS test error:', error.message);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting Connection Fix Tests...\n');

    const results = {
        healthCheck: await testHealthCheck(),
        sseConnection: await testSSEConnection(),
        authEndpoints: await testAuthEndpoints(),
        agentEndpoint: await testAgentEndpoint(),
        corsHeaders: await testCORSHeaders()
    };

    console.log('\n📊 Test Results Summary:');
    console.log('========================');

    let passedTests = 0;
    const totalTests = Object.keys(results).length;

    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${test}: ${status}`);
        if (passed) passedTests++;
    });

    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
        console.log('🎉 All connection fixes are working correctly!');
        process.exit(0);
    } else {
        console.log('⚠️ Some tests failed. Please check the issues above.');
        process.exit(1);
    }
}

// Run the tests
runAllTests().catch(error => {
    console.error('💥 Test runner error:', error);
    process.exit(1);
});
