<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Fix Test - Update 13 Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }
        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.info { color: #17a2b8; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 SSE Fix Test - "Update 13" Issue</h1>
        <p>This page tests the fix for the SSE connection issue that was causing "update 13" errors.</p>
        
        <div class="test-section">
            <h3>🔌 SSE Connection Test</h3>
            <div id="connection-status" class="status info">Ready to test</div>
            <button onclick="testSSEConnection()">Test SSE Connection</button>
            <button onclick="stopSSEConnection()" disabled id="stopBtn">Stop Connection</button>
            <div id="sse-log" class="log"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="test-results">
                <div id="endpoint-test" class="status info">Endpoint test: Not started</div>
                <div id="connection-test" class="status info">Connection test: Not started</div>
                <div id="streaming-test" class="status info">Streaming test: Not started</div>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let messageCount = 0;
        let testStartTime = null;

        function addLog(message, type = 'info') {
            const log = document.getElementById('sse-log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function testSSEConnection() {
            if (eventSource) {
                eventSource.close();
            }

            messageCount = 0;
            testStartTime = Date.now();
            
            addLog('🚀 Starting SSE connection test...', 'info');
            updateStatus('connection-status', 'Testing SSE connection...', 'info');
            updateStatus('endpoint-test', 'Testing endpoint accessibility...', 'info');
            
            // Test the new endpoint
            eventSource = new EventSource('/api/agent-stream');
            
            eventSource.onopen = function(event) {
                addLog('✅ SSE connection opened successfully!', 'success');
                updateStatus('connection-status', 'Connected - Receiving updates', 'success');
                updateStatus('endpoint-test', 'Endpoint accessible: ✅ PASS', 'success');
                updateStatus('connection-test', 'Connection established: ✅ PASS', 'success');
                
                document.getElementById('stopBtn').disabled = false;
            };

            eventSource.onmessage = function(event) {
                messageCount++;
                
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'connected') {
                        addLog(`🔌 Connected: ${data.message}`, 'success');
                    } else if (data.type === 'status') {
                        addLog(`📊 Status Update ${messageCount}: ${data.message}`, 'info');
                        
                        // Check if we're getting the "update X" messages
                        if (data.message.includes('update')) {
                            const updateMatch = data.message.match(/update (\d+)/);
                            if (updateMatch) {
                                const updateNumber = updateMatch[1];
                                addLog(`✅ Received update ${updateNumber} - SSE streaming working!`, 'success');
                                
                                if (messageCount >= 3) {
                                    updateStatus('streaming-test', 'Streaming working: ✅ PASS', 'success');
                                    addLog('🎉 All tests passed! The "update 13" issue is FIXED!', 'success');
                                }
                            }
                        }
                    } else if (data.type === 'timeout') {
                        addLog('⏰ Connection timeout (normal for Vercel)', 'info');
                    }
                    
                } catch (error) {
                    addLog(`❌ Error parsing message: ${error.message}`, 'error');
                }
            };

            eventSource.onerror = function(event) {
                addLog('❌ SSE connection error occurred', 'error');
                updateStatus('connection-status', 'Connection error', 'error');
                
                if (eventSource.readyState === EventSource.CLOSED) {
                    addLog('🔌 SSE connection closed', 'info');
                    updateStatus('connection-status', 'Connection closed', 'info');
                    document.getElementById('stopBtn').disabled = true;
                } else if (eventSource.readyState === EventSource.CONNECTING) {
                    addLog('🔄 SSE attempting to reconnect...', 'info');
                    updateStatus('connection-status', 'Reconnecting...', 'info');
                }
            };
        }

        function stopSSEConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                addLog('🛑 SSE connection stopped by user', 'info');
                updateStatus('connection-status', 'Connection stopped', 'info');
                document.getElementById('stopBtn').disabled = true;
            }
        }

        // Auto-start test when page loads
        window.addEventListener('load', function() {
            addLog('🌐 Page loaded, ready to test SSE fix', 'info');
            
            // Test the old endpoint to confirm it fails
            fetch('/api/agent/status/stream')
                .then(response => {
                    if (response.status === 404) {
                        addLog('✅ Old endpoint correctly returns 404 (as expected)', 'success');
                    } else {
                        addLog('⚠️ Old endpoint unexpectedly accessible', 'error');
                    }
                })
                .catch(error => {
                    addLog('✅ Old endpoint correctly fails (as expected)', 'success');
                });
        });
    </script>
</body>
</html>
