// Agent Status API endpoint for Vercel serverless deployment
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

// In-memory status storage for serverless environment
let agentStatus = {
    status: 'idle',
    message: 'Agent is ready',
    timestamp: new Date().toISOString()
};

// Vercel serverless function handler
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
    res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    try {
        if (req.method === 'GET') {
            // Return current agent status
            res.json({
                ...agentStatus,
                environment: 'serverless',
                timestamp: new Date().toISOString()
            });
        } else if (req.method === 'POST') {
            // Update agent status (for internal use)
            const { status, message, projectName, projectPath } = req.body;
            
            agentStatus = {
                status: status || agentStatus.status,
                message: message || agentStatus.message,
                projectName: projectName || agentStatus.projectName,
                projectPath: projectPath || agentStatus.projectPath,
                timestamp: new Date().toISOString()
            };

            res.json({
                success: true,
                message: 'Status updated',
                status: agentStatus
            });
        } else {
            res.status(405).json({ error: 'Method not allowed' });
        }
    } catch (error) {
        console.error('Agent status error:', error);
        res.status(500).json({
            error: 'Failed to handle agent status',
            details: error.message,
            timestamp: new Date().toISOString()
        });
    }
}
