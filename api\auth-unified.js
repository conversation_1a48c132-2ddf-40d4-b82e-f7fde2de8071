// Unified Authentication endpoint for Vercel serverless deployment
import bcrypt from 'bcryptjs';
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { UserStorage } from './storage.js';
import { FirebaseAuth } from './firebase-auth.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

const userStorage = new UserStorage();
const firebaseAuth = new FirebaseAuth();

// Helper functions
const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

const validatePassword = (password) => {
    return password && password.length >= 6;
};

// Set CORS headers
function setCorsHeaders(res) {
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');
}

// Login handler
async function handleLogin(req, res) {
    try {
        const { email, password } = req.body;

        // Validation
        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({ error: 'Invalid email format' });
        }

        // Get user
        const user = await userStorage.getUserByEmail(email);
        if (!user) {
            return res.status(401).json({ error: 'Invalid email or password' });
        }

        // Check password
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid email or password' });
        }

        // Return user data
        res.json({
            success: true,
            user: {
                id: user.id,
                email: user.email,
                provider: user.provider
            },
            message: 'Login successful'
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ 
            error: 'Login failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}

// Register handler
async function handleRegister(req, res) {
    try {
        const { email, password } = req.body;

        // Validation
        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({ error: 'Invalid email format' });
        }

        if (!validatePassword(password)) {
            return res.status(400).json({ error: 'Password must be at least 6 characters long' });
        }

        // Check if user already exists
        const existingUser = await userStorage.getUserByEmail(email);
        if (existingUser) {
            return res.status(400).json({ error: 'User already exists with this email' });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 12);

        // Create user
        const user = await userStorage.createUser({
            email,
            password: hashedPassword,
            provider: 'local',
            createdAt: new Date().toISOString()
        });

        // Return user data
        res.status(201).json({
            success: true,
            user: {
                id: user.id,
                email: user.email,
                provider: user.provider
            },
            message: 'Registration successful'
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ 
            error: 'Registration failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}

// Google OAuth handler
async function handleGoogleAuth(req, res) {
    try {
        const { idToken } = req.body;

        if (!idToken) {
            return res.status(400).json({ error: 'ID token is required' });
        }

        // Verify Firebase token
        const decodedToken = await firebaseAuth.verifyIdToken(idToken);
        const { email, name } = decodedToken;

        if (!email) {
            return res.status(400).json({ error: 'Email not found in token' });
        }

        // Check if user exists
        let user = await userStorage.getUserByEmail(email);

        if (!user) {
            // Create new user
            user = await userStorage.createUser({
                email,
                name: name || email.split('@')[0],
                provider: 'google',
                createdAt: new Date().toISOString()
            });
        }

        // Return user data
        res.json({
            success: true,
            user: {
                id: user.id,
                email: user.email,
                provider: user.provider
            },
            message: 'Google authentication successful'
        });

    } catch (error) {
        console.error('Google auth error:', error);
        res.status(401).json({ 
            error: 'Google authentication failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}

// Main handler
export default async function handler(req, res) {
    setCorsHeaders(res);

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // Route based on URL path
    const { url } = req;
    
    if (url.includes('/login') || req.body.action === 'login') {
        return handleLogin(req, res);
    } else if (url.includes('/register') || req.body.action === 'register') {
        return handleRegister(req, res);
    } else if (url.includes('/google') || req.body.action === 'google-auth') {
        return handleGoogleAuth(req, res);
    } else {
        // Default to login if no specific action
        return handleLogin(req, res);
    }
}
