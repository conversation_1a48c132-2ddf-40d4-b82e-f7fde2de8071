// Main API handler for Vercel serverless deployment
import express from 'express';
import session from 'express-session';
import cors from 'cors';
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

// Create Express app
const app = express();

// Enhanced CORS configuration for serverless
app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);

        const allowedOrigins = process.env.NODE_ENV === 'production'
            ? [
                process.env.VERCEL_URL,
                process.env.PRODUCTION_URL,
                'https://ai-coder-agentic-nclm6jpm-suhaibulk77s-projects.vercel.app',
                'https://ai-coder-agentic.vercel.app'
            ].filter(Boolean)
            : ['http://localhost:3000', 'http://127.0.0.1:3000'];

        if (allowedOrigins.indexOf(origin) !== -1 || !origin) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
    exposedHeaders: ['Set-Cookie']
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Enhanced session configuration for serverless with better error handling
app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        httpOnly: true
    },
    name: 'ai-coder-session'
}));

// Connection tracking for SSE
global.sseConnections = global.sseConnections || new Set();

// Initialize database and auth with better error handling
async function initializeServices() {
    try {
        // Import auth setup function
        const authModule = await import('./auth-unified.js');
        const database = (await import('./database.js')).default;

        // Initialize database connection
        await database.connect();

        return { database, authHandler: authModule.default };
    } catch (error) {
        console.error('Failed to initialize services:', error);
        // Don't throw error, allow app to continue with limited functionality
        return { database: null, authHandler: null };
    }
}

// Authentication middleware for protected routes
function requireAuth(req, res, next) {
    // For serverless deployment, we'll use a more flexible auth approach
    // Check for session or allow unauthenticated access for agent mode compatibility
    if (req.session && req.session.userId) {
        return next();
    }

    // For agent mode, allow unauthenticated access to maintain compatibility
    if (req.url.includes('/agent') || req.url.includes('/stream')) {
        console.log('🔓 Allowing unauthenticated access for agent mode compatibility');
        return next();
    }

    return res.status(401).json({ error: 'Authentication required' });
}

// Authentication middleware for protected routes
function requireAuth(req, res, next) {
    // For serverless deployment, we'll use a more flexible auth approach
    // Check for session or allow unauthenticated access for agent mode compatibility
    if (req.session && req.session.userId) {
        return next();
    }

    // For agent mode, allow unauthenticated access to maintain compatibility
    if (req.url.includes('/agent') || req.url.includes('/stream')) {
        console.log('🔓 Allowing unauthenticated access for agent mode compatibility');
        return next();
    }

    return res.status(401).json({ error: 'Authentication required' });
}

// Authentication routes
app.post('/api/auth/login', async (req, res) => {
    try {
        const { authHandler } = await initializeServices();
        if (!authHandler) {
            return res.status(500).json({ error: 'Authentication service unavailable' });
        }

        // Simulate the auth-unified handler
        req.url = '/login';
        return authHandler(req, res);
    } catch (error) {
        console.error('Login route error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

app.post('/api/auth/register', async (req, res) => {
    try {
        const { authHandler } = await initializeServices();
        if (!authHandler) {
            return res.status(500).json({ error: 'Authentication service unavailable' });
        }

        req.url = '/register';
        return authHandler(req, res);
    } catch (error) {
        console.error('Register route error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});

app.post('/api/auth/google', async (req, res) => {
    try {
        const { authHandler } = await initializeServices();
        if (!authHandler) {
            return res.status(500).json({ error: 'Authentication service unavailable' });
        }

        req.url = '/google';
        return authHandler(req, res);
    } catch (error) {
        console.error('Google auth route error:', error);
        res.status(500).json({ error: 'Google authentication failed' });
    }
});

app.get('/api/user', (req, res) => {
    if (!req.session || !req.session.userId) {
        return res.status(401).json({ error: 'Not authenticated' });
    }

    res.json(req.session.user || { id: req.session.userId });
});

app.post('/api/logout', (req, res) => {
    if (req.session) {
        req.session.destroy((err) => {
            if (err) {
                console.error('Logout error:', err);
                return res.status(500).json({ error: 'Logout failed' });
            }
            res.clearCookie('ai-coder-session');
            res.json({ message: 'Logged out successfully' });
        });
    } else {
        res.json({ message: 'Already logged out' });
    }
});

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const { database } = await initializeServices();
        const dbHealth = database ? await database.healthCheck() : { status: 'disabled' };
        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            database: dbHealth,
            environment: process.env.NODE_ENV || 'development',
            sseConnections: global.sseConnections ? global.sseConnections.size : 0
        });
    } catch (error) {
        res.status(500).json({
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Export the Express app for Vercel
export default app;