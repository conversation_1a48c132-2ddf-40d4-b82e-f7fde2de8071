// Comprehensive test script for Vercel deployment
// Run this script to test all functionality after deployment

import fetch from 'node-fetch';
import { config } from 'dotenv';

// Load environment variables
config();

const BASE_URL = process.env.VERCEL_URL || process.env.TEST_URL || 'http://localhost:3000';

console.log(`🧪 Testing Vercel deployment at: ${BASE_URL}`);

// Test configuration
const testConfig = {
    timeout: 30000, // 30 seconds
    retries: 3
};

// Test utilities
async function makeRequest(endpoint, options = {}) {
    const url = `${BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
        timeout: testConfig.timeout
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    console.log(`📡 Testing: ${options.method || 'GET'} ${url}`);
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        return {
            status: response.status,
            ok: response.ok,
            data: data
        };
    } catch (error) {
        console.error(`❌ Request failed: ${error.message}`);
        return {
            status: 0,
            ok: false,
            error: error.message
        };
    }
}

// Test functions
async function testHealthCheck() {
    console.log('\n🏥 Testing Health Check...');
    
    const result = await makeRequest('/api/health');
    
    if (result.ok && result.data.status === 'OK') {
        console.log('✅ Health check passed');
        console.log(`   Environment: ${result.data.environment}`);
        console.log(`   Database: ${result.data.database?.status || 'not configured'}`);
        console.log(`   Agent Mode: ${result.data.agent_mode?.status || 'unknown'}`);
        return true;
    } else {
        console.log('❌ Health check failed');
        console.log(`   Status: ${result.status}`);
        console.log(`   Error: ${result.data?.error || result.error}`);
        return false;
    }
}

async function testUserRegistration() {
    console.log('\n👤 Testing User Registration...');
    
    const testUser = {
        email: `test-${Date.now()}@example.com`,
        password: 'testpassword123'
    };
    
    const result = await makeRequest('/api/register', {
        method: 'POST',
        body: JSON.stringify(testUser)
    });
    
    if (result.ok && result.data.success) {
        console.log('✅ User registration passed');
        console.log(`   User ID: ${result.data.user.id}`);
        console.log(`   Email: ${result.data.user.email}`);
        return testUser;
    } else {
        console.log('❌ User registration failed');
        console.log(`   Status: ${result.status}`);
        console.log(`   Error: ${result.data?.error || result.error}`);
        return null;
    }
}

async function testUserLogin(testUser) {
    console.log('\n🔐 Testing User Login...');
    
    if (!testUser) {
        console.log('⏭️  Skipping login test (no test user)');
        return false;
    }
    
    const result = await makeRequest('/api/login', {
        method: 'POST',
        body: JSON.stringify(testUser)
    });
    
    if (result.ok && result.data.success) {
        console.log('✅ User login passed');
        console.log(`   User ID: ${result.data.user.id}`);
        console.log(`   Provider: ${result.data.user.provider}`);
        return true;
    } else {
        console.log('❌ User login failed');
        console.log(`   Status: ${result.status}`);
        console.log(`   Error: ${result.data?.error || result.error}`);
        return false;
    }
}

async function testAgentMode() {
    console.log('\n🤖 Testing Agent Mode...');
    
    const testPrompt = {
        prompt: 'Create a simple HTML page with a hello world message'
    };
    
    const result = await makeRequest('/api/agent', {
        method: 'POST',
        body: JSON.stringify(testPrompt)
    });
    
    if (result.ok && result.data.success) {
        console.log('✅ Agent mode passed');
        console.log(`   Status: ${result.data.status}`);
        console.log(`   Iterations: ${result.data.iterations}`);
        console.log(`   Results: ${result.data.results?.length || 0} steps`);
        return true;
    } else {
        console.log('❌ Agent mode failed');
        console.log(`   Status: ${result.status}`);
        console.log(`   Error: ${result.data?.error || result.error}`);
        return false;
    }
}

async function testAgentStatus() {
    console.log('\n📊 Testing Agent Status...');
    
    const result = await makeRequest('/api/agent-status');
    
    if (result.ok) {
        console.log('✅ Agent status passed');
        console.log(`   Status: ${result.data.status}`);
        console.log(`   Environment: ${result.data.environment}`);
        return true;
    } else {
        console.log('❌ Agent status failed');
        console.log(`   Status: ${result.status}`);
        console.log(`   Error: ${result.data?.error || result.error}`);
        return false;
    }
}

async function testSSEConnection() {
    console.log('\n📡 Testing SSE Connection...');
    
    // For Node.js testing, we'll just check if the endpoint responds
    const result = await makeRequest('/api/agent-stream');
    
    // SSE endpoints typically return 200 even for GET requests
    if (result.status === 200 || result.status === 405) {
        console.log('✅ SSE endpoint accessible');
        console.log('   Note: Full SSE testing requires browser environment');
        return true;
    } else {
        console.log('❌ SSE endpoint failed');
        console.log(`   Status: ${result.status}`);
        return false;
    }
}

async function testStaticFiles() {
    console.log('\n📁 Testing Static Files...');
    
    try {
        const response = await fetch(`${BASE_URL}/`);
        const html = await response.text();
        
        if (response.ok && html.includes('AI-Powered Code Editor')) {
            console.log('✅ Static files served correctly');
            console.log('   Main page loads successfully');
            return true;
        } else {
            console.log('❌ Static files failed');
            console.log(`   Status: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log('❌ Static files failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting Vercel Deployment Tests');
    console.log('=====================================');
    
    const results = {
        healthCheck: false,
        staticFiles: false,
        userRegistration: false,
        userLogin: false,
        agentMode: false,
        agentStatus: false,
        sseConnection: false
    };
    
    let testUser = null;
    
    try {
        // Run tests in sequence
        results.healthCheck = await testHealthCheck();
        results.staticFiles = await testStaticFiles();
        
        testUser = await testUserRegistration();
        results.userRegistration = !!testUser;
        
        results.userLogin = await testUserLogin(testUser);
        results.agentMode = await testAgentMode();
        results.agentStatus = await testAgentStatus();
        results.sseConnection = await testSSEConnection();
        
    } catch (error) {
        console.error('\n💥 Test runner error:', error.message);
    }
    
    // Summary
    console.log('\n📋 Test Results Summary');
    console.log('=======================');
    
    const testNames = {
        healthCheck: 'Health Check',
        staticFiles: 'Static Files',
        userRegistration: 'User Registration',
        userLogin: 'User Login',
        agentMode: 'Agent Mode',
        agentStatus: 'Agent Status',
        sseConnection: 'SSE Connection'
    };
    
    let passedTests = 0;
    let totalTests = Object.keys(results).length;
    
    for (const [key, passed] of Object.entries(results)) {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testNames[key]}`);
        if (passed) passedTests++;
    }
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Deployment is successful.');
        process.exit(0);
    } else {
        console.log('⚠️  Some tests failed. Check the deployment configuration.');
        process.exit(1);
    }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests().catch(error => {
        console.error('💥 Test execution failed:', error);
        process.exit(1);
    });
}

export { runAllTests, testConfig };
