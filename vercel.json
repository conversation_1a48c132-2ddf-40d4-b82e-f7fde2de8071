{"version": 2, "functions": {"api/**/*.js": {"maxDuration": 30}}, "rewrites": [{"source": "/agent/(.*)", "destination": "/public/agent/$1"}, {"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}