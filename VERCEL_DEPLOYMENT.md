# Complete Vercel Deployment Guide with Full Agent Mode

This guide provides step-by-step instructions for deploying the AI-Coder application to Vercel with **complete agent mode functionality preserved**.

## 🚀 Overview

This deployment maintains all agent mode capabilities including:
- ✅ Process spawning and command execution (serverless-adapted)
- ✅ Real-time Server-Sent Events (SSE) streaming
- ✅ VSCode integration features (serverless-compatible)
- ✅ All interactive agent functionalities
- ✅ Authentication with PostgreSQL database
- ✅ Google OAuth integration
- ✅ Session management

## 📋 Prerequisites

### 1. Required Accounts & Services

- **Vercel Account**: [vercel.com](https://vercel.com)
- **Neon Database**: [neon.tech](https://neon.tech) (PostgreSQL)
- **Google Cloud Console**: [console.cloud.google.com](https://console.cloud.google.com) (for OAuth)
- **Google AI Studio**: [aistudio.google.com](https://aistudio.google.com) (for Gemini API)
- **OpenRouter**: [openrouter.ai](https://openrouter.ai) (for additional models)

### 2. Required API Keys

1. **Gemini API Key** (for Agent Mode)
2. **OpenRouter API Key** (for Normal Mode)
3. **Firebase Service Account** (for Google OAuth)
4. **Neon Database URL** (for user storage)

## 🔧 Step 1: Database Setup

### Create Neon PostgreSQL Database

1. Go to [neon.tech](https://neon.tech) and create an account
2. Create a new project
3. Copy the connection string (format: `**********************************************`)
4. The database schema will be automatically created on first connection

## 🔑 Step 2: API Keys Setup

### Google Gemini API Key

1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the key for later use

### OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai)
2. Create an account and generate an API key
3. Copy the key for later use

### Firebase Setup (Google OAuth)

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project
3. Enable Authentication → Google provider
4. Go to Project Settings → Service Accounts
5. Generate a new private key (JSON file)
6. Extract the required values:
   - `project_id`
   - `client_email`
   - `private_key`

## 🚀 Step 3: Vercel Deployment

### Option A: Deploy via Vercel CLI

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy the Project**
   ```bash
   vercel
   ```

4. **Follow the prompts:**
   - Set up and deploy? `Y`
   - Which scope? Choose your account
   - Link to existing project? `N`
   - Project name: `ai-coder-agent`
   - Directory: `./` (current directory)

### Option B: Deploy via GitHub Integration

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Prepare for Vercel deployment"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository
   - Configure project settings

## ⚙️ Step 4: Environment Variables Configuration

In your Vercel dashboard, go to Project Settings → Environment Variables and add:

### Required Variables

```env
NODE_ENV=production
SESSION_SECRET=your-super-secure-random-string-at-least-32-characters-long
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require
USE_DATABASE=true
GEMINI_API_KEY=your-gemini-api-key-here
OPENROUTER_API_KEY=your-openrouter-api-key
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
```

### Optional Variables

```env
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000
```

## 🧪 Step 5: Testing Deployment

### 1. Basic Functionality Test

1. Visit your deployed URL
2. Test user registration/login
3. Try normal mode code generation
4. Verify authentication works

### 2. Agent Mode Test

1. Toggle to Agent Mode (🤖 button)
2. Try an agent prompt: "Create a simple todo app with HTML, CSS, and JavaScript"
3. Verify the agent processes the request
4. Check that results are displayed properly

### 3. Real-time Features Test

1. Open browser developer tools
2. Monitor Network tab for SSE connections
3. Verify real-time status updates work

## 🔍 Step 6: Monitoring & Debugging

### Vercel Function Logs

1. Go to Vercel Dashboard → Your Project → Functions
2. Click on any function to view logs
3. Monitor for errors or performance issues

### Common Issues & Solutions

1. **Environment Variables Not Loading**
   - Verify all variables are set in Vercel dashboard
   - Redeploy after adding variables

2. **Database Connection Issues**
   - Check DATABASE_URL format
   - Verify Neon database is accessible
   - Check connection limits

3. **Agent Mode Not Working**
   - Verify GEMINI_API_KEY is correct
   - Check function timeout limits (30s max)
   - Monitor function logs for errors

4. **SSE Connection Issues**
   - Verify browser supports EventSource
   - Check for CORS issues
   - Monitor connection timeouts

## 🎯 Step 7: Production Optimization

### Performance Optimization

1. **Function Configuration**
   - Functions are configured for 30s timeout
   - Memory optimized for AI processing
   - Connection pooling for database

2. **Caching Strategy**
   - Static assets cached via Vercel CDN
   - API responses optimized for performance

3. **Database Optimization**
   - Connection pooling (max 1 for serverless)
   - Optimized queries with indexes
   - Automatic schema initialization

### Security Configuration

1. **CORS Settings**
   - Configured for production domains
   - Credentials support enabled

2. **Session Security**
   - Secure cookies in production
   - Session timeout configured
   - CSRF protection enabled

3. **API Security**
   - Rate limiting considerations
   - Input validation
   - Error handling

## 🔄 Step 8: Continuous Deployment

### Automatic Deployments

1. **GitHub Integration**
   - Automatic deployments on push to main
   - Preview deployments for pull requests
   - Environment-specific configurations

2. **Environment Management**
   - Production environment variables
   - Preview environment settings
   - Development environment setup

## 📊 Step 9: Monitoring & Analytics

### Built-in Monitoring

1. **Vercel Analytics**
   - Function performance metrics
   - Error tracking
   - Usage statistics

2. **Database Monitoring**
   - Neon dashboard metrics
   - Connection monitoring
   - Query performance

### Custom Monitoring

1. **Health Checks**
   - `/api/health` endpoint
   - Database connectivity
   - Service status monitoring

2. **Error Tracking**
   - Function error logs
   - Client-side error handling
   - Performance monitoring

## ✅ Deployment Checklist

- [ ] Database created and configured
- [ ] All API keys obtained
- [ ] Environment variables set in Vercel
- [ ] Project deployed successfully
- [ ] Basic functionality tested
- [ ] Agent mode tested
- [ ] Authentication tested
- [ ] Real-time features tested
- [ ] Production optimizations applied
- [ ] Monitoring configured

## 🆘 Support & Troubleshooting

### Common Commands

```bash
# Redeploy
vercel --prod

# Check deployment status
vercel ls

# View function logs
vercel logs

# Environment variables
vercel env ls
```

### Getting Help

1. Check Vercel documentation
2. Review function logs
3. Test locally first
4. Check environment variables
5. Verify API key permissions

## 🎉 Success!

Your AI-Coder application with full agent mode functionality is now deployed on Vercel! The serverless architecture maintains all the original capabilities while providing scalability and reliability.

### Key Features Preserved:

- ✅ Complete agent mode functionality
- ✅ Real-time status streaming
- ✅ User authentication & sessions
- ✅ Database integration
- ✅ Google OAuth support
- ✅ All interactive features

The deployment is production-ready and optimized for serverless performance while maintaining the full agent experience.
