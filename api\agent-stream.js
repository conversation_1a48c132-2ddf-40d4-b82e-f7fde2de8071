// Agent Status Streaming API endpoint for Vercel serverless deployment
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import session from 'express-session';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
config({ path: path.join(__dirname, '..', '.env') });

// Global status storage for serverless environment
let globalAgentStatus = {
    status: 'idle',
    message: 'Agent ready',
    timestamp: new Date().toISOString(),
    environment: 'serverless'
};

// Active SSE connections - use global to persist across function calls
global.sseConnections = global.sseConnections || new Set();

// Session configuration for serverless
const sessionConfig = {
    secret: process.env.SESSION_SECRET || 'your-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        httpOnly: true
    },
    name: 'ai-coder-session'
};

// Initialize session middleware
const sessionMiddleware = session(sessionConfig);

// Vercel serverless function handler for SSE
export default async function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,POST');
    res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method === 'POST') {
        // Handle status updates from agent execution
        try {
            const statusUpdate = req.body;
            const updatedStatus = {
                ...globalAgentStatus,
                ...statusUpdate,
                timestamp: new Date().toISOString(),
                environment: 'serverless'
            };

            // Update both local and global status
            globalAgentStatus = updatedStatus;
            global.agentStatus = updatedStatus;

            // Broadcast to all active connections
            broadcastToConnections(updatedStatus);

            res.json({ success: true, message: 'Status updated' });
            return;
        } catch (error) {
            console.error('Error updating status:', error);
            res.status(500).json({ error: 'Failed to update status' });
            return;
        }
    }

    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // Initialize session for this request
    try {
        await new Promise((resolve, reject) => {
            sessionMiddleware(req, res, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    } catch (sessionError) {
        console.warn('Session initialization failed, continuing without session:', sessionError.message);
    }

    try {
        // Set up SSE headers with enhanced configuration
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',
            'Access-Control-Allow-Credentials': 'true',
            'X-Accel-Buffering': 'no' // Disable nginx buffering
        });

        // Add this connection to global connections
        const connectionId = Date.now() + Math.random();
        const connection = {
            id: connectionId,
            res,
            userId: req.session?.userId || 'anonymous',
            timestamp: new Date().toISOString()
        };
        global.sseConnections.add(connection);

        console.log(`📡 SSE client connected: ${connectionId} (${global.sseConnections.size} total)`);

        // Send initial connection message with enhanced info
        res.write(`data: ${JSON.stringify({
            type: "connected",
            message: "SSE connection established",
            environment: "serverless",
            connectionId: connectionId,
            timestamp: new Date().toISOString()
        })}\n\n`);

        // Send current status immediately - check global status first
        const currentStatus = global.agentStatus || globalAgentStatus;
        res.write(`data: ${JSON.stringify({ type: 'status', ...currentStatus })}\n\n`);

        // Enhanced client disconnect handling
        req.on('close', () => {
            cleanupConnection(connection);
        });

        req.on('error', (error) => {
            console.error(`📡 SSE client error: ${connectionId}`, error);
            cleanupConnection(connection);
        });

        // Keep connection alive for up to 25 seconds (Vercel limit is 30s)
        const timeoutId = setTimeout(() => {
            try {
                res.write('data: {"type": "timeout", "message": "Connection timeout - will reconnect"}\n\n');
                res.end();
            } catch (err) {
                console.error('Error sending timeout message:', err);
            }
            cleanupConnection(connection);
        }, 25000);

        // Store timeout ID for cleanup
        connection.timeoutId = timeoutId;

    } catch (error) {
        console.error('SSE streaming error:', error);
        res.status(500).json({
            error: 'Failed to establish SSE connection',
            details: error.message,
            timestamp: new Date().toISOString()
        });
    }
}

// Function to broadcast status updates to all active connections
function broadcastToConnections(statusData) {
    const message = `data: ${JSON.stringify({ type: 'status', ...statusData })}\n\n`;
    const deadConnections = [];

    global.sseConnections.forEach(connection => {
        try {
            if (connection.res && !connection.res.destroyed) {
                connection.res.write(message);
            } else {
                deadConnections.push(connection);
            }
        } catch (error) {
            console.error(`📡 Error sending to connection ${connection.id}:`, error.message);
            deadConnections.push(connection);
        }
    });

    // Clean up dead connections
    deadConnections.forEach(connection => global.sseConnections.delete(connection));

    if (global.sseConnections.size > 0) {
        console.log(`📡 Broadcasted status to ${global.sseConnections.size} connections`);
    }
}

// Enhanced connection cleanup function
function cleanupConnection(connection) {
    if (connection && global.sseConnections.has(connection)) {
        // Clear timeout if it exists
        if (connection.timeoutId) {
            clearTimeout(connection.timeoutId);
        }

        global.sseConnections.delete(connection);
        console.log(`📡 SSE client disconnected: ${connection.id} (${global.sseConnections.size} total)`);
    }
}
