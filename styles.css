/* Define CSS Variables for colors */
:root {
  --bg-gradient-start: #111827;
  --bg-gradient-end: #0b141e;
  --text-primary: #ecf0f1;
  --text-secondary: #95a5a6;
  --text-muted: #bdc3c7;
  --header-bg: rgba(0, 0, 0, 0.4);
  --border-color: #2d3748;
  --input-bg: #1a202c;
  --input-border: #4a5568;
  --input-text: #e0e6ed;
  --input-placeholder: #718096;
  --button-bg-start: #4285f4;
  --button-bg-end: #1976d2;
  --button-text: white;
  --link-color: #64b5f6;
  --link-hover: #81d4fa;
  --shadow-color: rgba(0, 0, 0, 0.6);
  --focus-glow: rgba(66, 139, 202, 0.6);
  --example-bg: rgba(255, 255, 255, 0.03);
  --example-button-bg: rgba(66, 133, 244, 0.1);
  --example-button-hover-bg: rgba(66, 133, 244, 0.25);
  --copy-button-bg: rgba(66, 133, 244, 0.15);
  --copy-button-border: #4285f4;
  --copy-button-hover-bg: rgba(66, 133, 244, 0.3);
  --copy-button-copied-bg: rgba(76, 175, 80, 0.2);
  --copy-button-copied-color: #81c784;
  --copy-button-copied-border: #81c784;
  --select-bg: #2d3748;
  --select-text: #ecf0f1;
  --select-border: #4a5568;
  --select-option-bg: #1a202c;
  --select-arrow: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23ecf0f1' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
}

body.agent-mode {
  --bg-gradient-start: #1a0d2e;
  --bg-gradient-end: #16213e;
  --border-color: #4a148c;
  --input-border: #6a1b9a;
  background: radial-gradient(
    circle,
    var(--bg-gradient-start),
    var(--bg-gradient-end)
  );
  animation: agentGlow 3s ease-in-out infinite alternate;
}

@keyframes agentGlow {
  0% {
    box-shadow: inset 0 0 50px rgba(74, 20, 140, 0.1);
  }
  100% {
    box-shadow: inset 0 0 50px rgba(74, 20, 140, 0.3);
  }
}

body.agent-mode .logo {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: logoShimmer 2s ease-in-out infinite alternate;
}

@keyframes logoShimmer {
  0% {
    text-shadow: 0 0 10px rgba(156, 39, 176, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(233, 30, 99, 0.8);
  }
}

body.agent-mode header {
  border-bottom: 1px solid rgba(156, 39, 176, 0.3);
  background: rgba(74, 20, 140, 0.2);
}

body.agent-mode h1 {
  background: linear-gradient(135deg, #9c27b0, #e91e63, #ff9800);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleGlow 2s ease-in-out infinite alternate;
}

body.agent-mode .example-prompts {
  background: linear-gradient(
    135deg,
    rgba(156, 39, 176, 0.1),
    rgba(233, 30, 99, 0.1)
  );
  border: 1px solid rgba(156, 39, 176, 0.3);
  box-shadow: 0 0 20px rgba(156, 39, 176, 0.2);
}

body.agent-mode .example-prompts h4 {
  color: #e91e63;
  text-shadow: 0 0 5px rgba(233, 30, 99, 0.5);
}

body.agent-mode .example-prompts button {
  background: linear-gradient(
    135deg,
    rgba(156, 39, 176, 0.2),
    rgba(233, 30, 99, 0.2)
  );
  border: 1px solid #9c27b0;
  color: #e91e63;
}

body.agent-mode .example-prompts button:hover {
  background: linear-gradient(
    135deg,
    rgba(156, 39, 176, 0.4),
    rgba(233, 30, 99, 0.4)
  );
  box-shadow: 0 0 15px rgba(233, 30, 99, 0.6);
  transform: translateY(-2px);
}

body.agent-mode textarea {
  border: 1px solid rgba(156, 39, 176, 0.5);
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.2);
}

body.agent-mode textarea:focus {
  box-shadow: 0 0 0 4px rgba(156, 39, 176, 0.4);
  border-color: #9c27b0;
}

body.agent-mode #generateButton {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
  box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4);
}

body.agent-mode #generateButton:hover {
  background: linear-gradient(135deg, #8e24aa, #d81b60);
  box-shadow: 0 6px 20px rgba(233, 30, 99, 0.6);
  transform: translateY(-2px);
}

body.agent-mode .output-container {
  border: 1px solid rgba(156, 39, 176, 0.3);
  box-shadow: 0 0 20px rgba(156, 39, 176, 0.2);
}

/* Agent Mode Output Container Styling */
.agent-output-container .output-header {
  background: linear-gradient(
    135deg,
    rgba(156, 39, 176, 0.2),
    rgba(233, 30, 99, 0.2)
  );
  border-bottom: 1px solid rgba(156, 39, 176, 0.3);
}

.agent-output-container .output-title {
  color: #e91e63;
  font-weight: 700;
  text-shadow: 0 0 5px rgba(233, 30, 99, 0.3);
}

body.agent-mode .agent-output-container {
  border: 2px solid rgba(156, 39, 176, 0.4);
  box-shadow: 0 0 25px rgba(156, 39, 176, 0.3);
  background: var(--input-bg);
}

body.agent-mode .agent-output-container .copy-button {
  border: 1px solid #9c27b0;
  color: #e91e63;
  background: rgba(156, 39, 176, 0.1);
}

body.agent-mode .agent-output-container .copy-button:hover {
  background: rgba(156, 39, 176, 0.2);
  color: #ff4081;
  box-shadow: 0 0 10px rgba(233, 30, 99, 0.5);
}

/* Normal Mode Output Container remains default styling */

body.light-theme {
  --bg-gradient-start: #f4f7f9;
  --bg-gradient-end: #e9edf1;
  --text-primary: #2c3e50;
  --text-secondary: #5a6a7a;
  --text-muted: #7f8c8d;
  --header-bg: rgba(255, 255, 255, 0.8);
  --border-color: #dce4ec;
  --input-bg: #ffffff;
  --input-border: #ced4da;
  --input-text: #343a40;
  --input-placeholder: #adb5bd;
  --button-bg-start: #3498db;
  --button-bg-end: #2980b9;
  --button-text: white;
  --link-color: #2980b9;
  --link-hover: #1f618d;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --focus-glow: rgba(52, 152, 219, 0.4);
  --example-bg: rgba(0, 0, 0, 0.03);
  --example-button-bg: rgba(52, 152, 219, 0.1);
  --example-button-hover-bg: rgba(52, 152, 219, 0.2);
  --copy-button-bg: rgba(52, 152, 219, 0.15);
  --copy-button-border: #3498db;
  --copy-button-hover-bg: rgba(52, 152, 219, 0.3);
  --copy-button-copied-bg: rgba(46, 204, 113, 0.2);
  --copy-button-copied-color: #27ae60;
  --copy-button-copied-border: #2ecc71;
  --select-bg: #ffffff;
  --select-text: #343a40;
  --select-border: #ced4da;
  --select-option-bg: #f8f9fa;
  --select-arrow: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23343a40' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: radial-gradient(
    circle,
    var(--bg-gradient-start),
    var(--bg-gradient-end)
  );
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  justify-content: center; /* Add this to vertically center content */
  box-sizing: border-box;
}

header {
  background: var(--header-bg);
  width: 100%;
  padding: 25px 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 1px 5px var(--shadow-color);
}

.logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--link-color);
  text-shadow: 0 0 10px var(--link-color);
}

/* Desktop Navigation */
.desktop-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
}

.desktop-nav ul li {
  margin-left: 30px;
}

.desktop-nav ul li a {
  color: var(--text-muted);
  text-decoration: none;
  font-size: 1.1rem;
  position: relative;
  transition: color 0.3s ease, text-shadow 0.3s ease;
}

.desktop-nav ul li a::before {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: var(--link-hover);
  visibility: hidden;
  transition: width 0.3s ease-in-out, visibility 0s linear 0.3s;
}

.desktop-nav ul li a:hover {
  color: var(--link-hover);
  text-shadow: 0 0 5px var(--link-hover);
}

.desktop-nav ul li a:hover::before {
  visibility: visible;
  width: 100%;
  transition: width 0.3s ease-in-out;
}

/* Mobile Menu Button */
.menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px 10px;
  margin-right: 10px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Side Navigation */
.sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  background-color: var(--bg-gradient-start);
  overflow-x: hidden;
  transition: 0.3s;
  box-shadow: 2px 0 10px var(--shadow-color);
  display: flex;
  flex-direction: column;
}

.sidenav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.sidenav-logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--link-color);
  text-shadow: 0 0 10px var(--link-color);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 2rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: var(--link-hover);
}

.sidenav-content {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.sidenav ul {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
}

.sidenav ul li {
  margin-bottom: 15px;
}

.sidenav ul li a {
  color: var(--text-primary);
  text-decoration: none;
  font-size: 1.2rem;
  transition: color 0.3s ease;
  display: block;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.sidenav ul li a:hover {
  color: var(--link-hover);
}

.sidenav-actions {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px 0;
}

.sidenav-actions .download-button {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
}

/* Overlay */
.overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.main-content {
  width: 90%;
  max-width: 1200px;
  padding: 40px 40px; /* Optional: Slightly reduce top/bottom padding */
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
}

h1 {
  font-size: 3.6rem;
  margin-bottom: 10px;
  color: var(--text-primary);
  text-align: left;
  font-weight: 700;
  text-shadow: 0 0 8px var(--shadow-color);
}

.agent-mode-indicator {
  background: linear-gradient(135deg, #9c27b0, #e91e63);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  animation: pulse 2s infinite;
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
  }
  50% {
    box-shadow: 0 0 25px rgba(233, 30, 99, 0.8);
  }
  100% {
    box-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
  }
}

p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  margin-bottom: 45px;
  text-align: left;
  line-height: 1.8;
}

.input-area {
  width: 100%;
  display: flex;
  gap: 25px;
}

textarea {
  flex-grow: 1;
  height: 180px;
  background: var(--input-bg);
  color: var(--input-text);
  border: 1px solid var(--input-border);
  padding: 20px;
  font-size: 16px;
  border-radius: 12px;
  box-sizing: border-box;
  font-family: "Fira Code", monospace;
  resize: vertical;
  box-shadow: 0 4px 10px var(--shadow-color);
}

textarea::placeholder {
  color: var(--input-placeholder);
}

button {
  padding: 18px 40px;
  background: linear-gradient(
    135deg,
    var(--button-bg-start),
    var(--button-bg-end)
  );
  color: var(--button-text);
  border: none;
  cursor: pointer;
  border-radius: 12px;
  font-size: 1.2rem;
  transition: background 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 10px var(--shadow-color);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
}

/* Base styling for the generate button */
#generateButton {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Special styling when a Gemini model is selected */
.gemini-model-selected #generateButton {
  background: linear-gradient(135deg, #4285f4, #0d47a1);
  transition: all 0.2s ease; /* Faster transition for Gemini models */
}

#generateButton::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

#generateButton:hover::after {
  left: 100%;
}

/* Flash effect when generating code */
#generateButton.generating {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
  }
}

button:hover {
  background: linear-gradient(
    135deg,
    var(--button-bg-end),
    var(--button-bg-start)
  );
  box-shadow: 0 5px 12px var(--shadow-color);
}

button:active {
  box-shadow: 0 2px 6px var(--shadow-color);
}

.output-container {
  display: none; /* Hide by default */
  width: 100%;
  background: var(--input-bg);
  border-radius: 12px;
  margin-top: 40px;
  box-sizing: border-box;
  overflow: hidden;
  box-shadow: 0 4px 10px var(--shadow-color);
}

.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid var(--border-color);
  gap: 15px;
}

.output-title {
  color: var(--text-muted);
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.model-selector label {
  color: var(--text-muted);
  font-size: 0.85rem;
  white-space: nowrap;
}

.model-selector select {
  background: var(--select-bg);
  color: var(--select-text);
  border: 1px solid var(--select-border);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.85rem;
  cursor: pointer;
}

.copy-button {
  padding: 6px 12px;
  background: var(--copy-button-bg);
  color: var(--link-color);
  border: 1px solid var(--copy-button-border);
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.copy-button:hover {
  background: var(--copy-button-hover-bg);
  box-shadow: 0 0 5px var(--link-color);
}

.copy-button.copied {
  background: var(--copy-button-copied-bg);
  color: var(--copy-button-copied-color);
  border-color: var(--copy-button-copied-border);
}

pre {
  margin: 0;
  padding: 25px;
  overflow-x: auto;
  overflow-y: auto;
  max-height: 500px; /* Set a max height to ensure scrollbar appears */
  border-radius: 0 0 12px 12px;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: var(--border-color) transparent; /* For Firefox */
}

pre::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

pre::-webkit-scrollbar-track {
  background: transparent;
}

pre::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
}

pre::-webkit-scrollbar-thumb:hover {
  background-color: var(--link-color);
}

code {
  font-family: "Fira Code", monospace;
  display: block;
  color: var(--input-text);
  font-size: 15px;
  line-height: 1.7;
  white-space: pre-wrap;
  word-break: break-all;
}

textarea:focus,
button:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 4px var(--focus-glow);
}

.download-button {
  background: none;
  color: var(--link-color);
  border: 1px solid var(--link-color);
  padding: 14px 26px;
  border-radius: 12px;
  cursor: pointer;
  text-decoration: none;
  font-size: 1.1rem;
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
  text-shadow: 0 0 3px var(--link-color);
}

.download-button:hover {
  background-color: var(--example-button-bg);
  box-shadow: 0 3px 6px var(--shadow-color);
}

.header-actions {
  display: flex;
  align-items: center;
}

.header-actions a {
  margin-left: 30px;
}

.input-area select {
  background-color: var(--select-bg);
  color: var(--select-text);
  border: 1px solid var(--select-border);
  border-radius: 12px;
  padding: 18px 40px 18px 20px;
  font-size: 16px;
  font-family: inherit;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: var(--select-arrow);
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 16px 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  height: auto;
  box-sizing: border-box;
  align-self: stretch;
}

.input-area select option {
  background-color: var(--select-option-bg);
  color: var(--select-text);
}

.input-area select:focus {
  outline: none;
  border-color: var(--link-color);
  box-shadow: 0 0 0 4px var(--focus-glow);
}

.input-area select:hover {
  border-color: var(--link-hover);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: baseline;
  padding: 40px 0;
  font-size: 1.1rem;
  color: var(--text-secondary);
}

/* Optimized loading animation for Flash model */
.flash-loading {
  padding: 20px 0;
  color: #4285f4; /* Google Blue */
  font-size: 1rem;
  background: linear-gradient(
    to right,
    rgba(66, 133, 244, 0.1),
    rgba(0, 0, 0, 0)
  );
  border-radius: 8px;
  transition: all 0.3s ease;
}

.flash-loading span:first-child {
  font-weight: 600;
  text-shadow: 0 0 5px rgba(66, 133, 244, 0.7);
}

/* Animation for other Gemini models */
.gemini-loading {
  padding: 20px 0;
  color: #4285f4; /* Google Blue */
  font-size: 1rem;
  background: linear-gradient(
    to right,
    rgba(66, 133, 244, 0.05),
    rgba(0, 0, 0, 0)
  );
  border-radius: 8px;
  transition: all 0.3s ease;
}

.gemini-loading span:first-child {
  font-weight: 600;
  text-shadow: 0 0 3px rgba(66, 133, 244, 0.5);
}

.loading-dot {
  animation: blink 0.8s infinite both; /* Faster animation for Flash model */
  font-size: 1.5rem;
  line-height: 1;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.1s; /* Faster delay for Flash model */
}

.loading-dot:nth-child(3) {
  animation-delay: 0.2s; /* Faster delay for Flash model */
}

@keyframes blink {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}

/* Agent Mode Loading Animation */
.agent-loading {
  color: #9c27b0;
  font-weight: bold;
}

.agent-loading .loading-dot {
  animation: agentDotPulse 1.5s infinite ease-in-out;
  color: #e91e63;
}

@keyframes agentDotPulse {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Agent Success Styling */
.agent-success {
  background: linear-gradient(
    135deg,
    rgba(156, 39, 176, 0.1),
    rgba(233, 30, 99, 0.1)
  );
  border: 2px solid rgba(156, 39, 176, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin: 10px 0;
  box-shadow: 0 4px 20px rgba(156, 39, 176, 0.2);
}

.agent-success h3 {
  color: #e91e63;
  margin: 0 0 15px 0;
  font-size: 1.5em;
  text-shadow: 0 0 5px rgba(233, 30, 99, 0.3);
}

.agent-success p {
  margin: 8px 0;
  color: var(--text-color);
  line-height: 1.6;
}

.agent-success p strong {
  color: #9c27b0;
  font-weight: bold;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  padding: 10px;
  background: rgba(156, 39, 176, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(156, 39, 176, 0.2);
}

.agent-info {
  margin-top: 10px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  font-family: "Courier New", monospace;
}

.agent-info small {
  color: var(--text-secondary);
  font-size: 0.85em;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  padding: 10px;
  background: rgba(156, 39, 176, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(156, 39, 176, 0.2);
}

.status-indicator {
  display: inline-block;
  font-size: 1.2em;
  transition: all 0.3s ease;
  animation: statusPulse 2s infinite;
}

.status-indicator.status-starting,
.status-indicator.status-processing {
  color: #ffa500;
  animation: statusBounce 1.5s infinite;
}

.status-indicator.status-thinking,
.status-indicator.status-executing {
  color: #00bfff;
  animation: statusRotate 2s linear infinite;
}

.status-indicator.status-opening_vscode,
.status-indicator.status-finalizing {
  color: #32cd32;
  animation: statusGlow 1s infinite alternate;
}

.status-indicator.status-completed {
  color: #00ff00;
  animation: statusSuccess 0.6s ease-out;
}

.status-indicator.status-error {
  color: #ff0000;
  animation: statusError 0.5s ease-out;
}

@keyframes statusBounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
}

@keyframes statusRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes statusGlow {
  0% {
    box-shadow: 0 0 5px currentColor;
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 15px currentColor;
    transform: scale(1.1);
  }
}

@keyframes statusSuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1.2);
  }
}

@keyframes statusError {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.error-message {
  color: #e74c3c;
  display: block;
  text-align: left;
  padding: 15px 20px;
  margin: 10px;
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 4px solid #e74c3c;
  font-family: "Segoe UI", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  border-radius: 4px;
}

#output:not([data-highlighted]) {
  background: none;
  padding: 0;
}

pre:has(.loading-container),
pre:has(.error-message) {
  padding: 0;
}

.example-prompts {
  margin-bottom: 30px;
  padding: 20px;
  background: var(--example-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.example-prompts h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--text-muted);
  font-weight: 600;
}

.example-prompts button {
  background: var(--example-button-bg);
  color: var(--link-color);
  border: 1px solid var(--copy-button-border);
  padding: 8px 15px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-prompts button:hover {
  background: var(--example-button-hover-bg);
  box-shadow: 0 0 5px var(--link-color);
}

.theme-toggle-button {
  background: none;
  border: 1px solid var(--link-color);
  color: var(--link-color);
  padding: 5px 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  transition: all 0.3s ease;
  margin-right: 15px;
}

.theme-toggle-button:hover {
  background: var(--example-button-bg);
  box-shadow: 0 0 5px var(--link-color);
}

.agent-mode-toggle-button {
  background: none;
  border: 1px solid var(--link-color);
  color: var(--link-color);
  padding: 5px 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  transition: all 0.3s ease;
  margin-right: 15px;
}

.agent-mode-toggle-button:hover {
  background: var(--example-button-bg);
  box-shadow: 0 0 5px var(--link-color);
}

.agent-mode-toggle-button.active {
  background: var(--button-bg-start);
  color: white;
  border-color: var(--button-bg-start);
  box-shadow: 0 0 10px rgba(66, 133, 244, 0.5);
}

.agent-mode-toggle-button.auth-required {
  opacity: 0.6;
  border-color: rgba(255, 193, 7, 0.5);
  color: #ffc107;
  position: relative;
}

.agent-mode-toggle-button.auth-required::after {
  content: "🔒";
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 0.7em;
  background: #ffc107;
  color: #000;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: authPulse 2s infinite;
}

@keyframes authPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

body.light-theme .hljs {
  background: #f8f9fa;
  color: #212529;
}
body.light-theme .hljs-keyword,
body.light-theme .hljs-selector-tag,
body.light-theme .hljs-literal,
body.light-theme .hljs-section,
body.light-theme .hljs-link {
  color: #007bff;
}
body.light-theme .hljs-string,
body.light-theme .hljs-symbol,
body.light-theme .hljs-bullet,
body.light-theme .hljs-addition {
  color: #28a745;
}
body.light-theme .hljs-comment,
body.light-theme .hljs-quote,
body.light-theme .hljs-deletion {
  color: #6c757d;
}
body.light-theme .hljs-attribute,
body.light-theme .hljs-attr {
  color: #fd7e14;
}

/* Auth Modal Styles */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6); /* Darker overlay */
  backdrop-filter: blur(10px); /* Increased blur */
  display: flex; /* Changed from none to flex for direct animation */
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
  opacity: 0; /* Start fully transparent */
  visibility: hidden; /* Start hidden */
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out; /* Smooth transition */
}

.auth-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.auth-modal {
  position: relative;
  max-width: 540px; /* Increased width further to better match screenshot */
  width: 100%;
  max-height: 95vh;
  overflow-y: auto;
  /* Removed animation here, handled by overlay */
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-25px) scale(0.98); /* Adjusted animation */
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.auth-modal-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: transparent;
  border: none;
  font-size: 20px; /* Slightly smaller font to ensure perfect circle */
  color: var(--text-secondary);
  cursor: pointer;
  z-index: 1001;
  width: 30px; /* Slightly larger to ensure perfect circle */
  height: 30px; /* Match width exactly */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%; /* Perfect circle */
  transition: all 0.2s ease;
  font-weight: 300;
  line-height: 1;
  opacity: 0.7;
  font-family: Arial, sans-serif; /* Ensure consistent font rendering */
  flex-shrink: 0; /* Prevent shrinking */
}

.auth-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  opacity: 1;
  transform: scale(1.1);
}

.auth-modal-close:active {
  transform: scale(0.95);
}

body.light-theme .auth-modal-close {
  background: transparent;
  opacity: 0.6;
}

body.light-theme .auth-modal-close:hover {
  background: rgba(0, 0, 0, 0.08);
  color: var(--text-primary);
  opacity: 1;
}

.auth-card {
  background: var(--input-bg); /* Use input-bg for consistency */
  border-radius: 12px; /* Softer radius */
  border: 1px solid var(--border-color);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.35); /* Enhanced shadow */
  overflow: hidden;
  animation: modalSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards; /* Apply animation to card */
}

/* Auth Header */
.auth-header {
  text-align: center;
  padding: 32px 28px 0 28px; /* Adjusted padding */
}

.auth-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 20px; /* Increased margin */
}

.sparkles-icon {
  width: 52px; /* Larger icon */
  height: 52px;
  background: linear-gradient(
    135deg,
    var(--button-bg-start),
    var(--button-bg-end)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px; /* Larger sparkle */
  color: white;
  box-shadow: 0 6px 15px rgba(66, 133, 244, 0.35); /* Softer shadow */
}

.auth-title {
  font-size: 26px; /* Larger title */
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 10px; /* Adjusted margin */
  line-height: 1.3;
}

.brand-text {
  background: linear-gradient(
    135deg,
    var(--button-bg-start),
    var(--button-bg-end)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  font-size: 15px; /* Slightly larger subtitle */
  color: var(--text-secondary);
  margin-bottom: 0;
  line-height: 1.5;
}

/* Auth Tabs */
.auth-tabs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  border-bottom: 1px solid var(--border-color);
  margin-top: 28px; /* Adjusted margin */
}

.auth-tab {
  background: none;
  border: none;
  padding: 18px 16px; /* Adjusted padding */
  font-size: 16px;
  color: var(--text-secondary);
  cursor: pointer;
  position: relative;
  transition: all 0.25s ease; /* Smoother transition */
  font-weight: 600; /* Bolder tabs */
  text-align: center;
}

.auth-tab:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.03); /* Subtle hover background */
}
body.light-theme .auth-tab:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.auth-tab.active {
  color: var(--button-bg-start);
}

.auth-tab.active::after {
  content: "";
  position: absolute;
  bottom: -1px; /* Align with border */
  left: 0;
  right: 0;
  height: 3px; /* Thicker indicator */
  background: var(--button-bg-start);
  border-radius: 3px 3px 0 0; /* Rounded top corners */
}

/* Highlight effect for tab transitions */
.auth-tab.highlight {
  animation: tabHighlight 0.6s ease-out;
}

@keyframes tabHighlight {
  0% {
    background-color: rgba(66, 133, 244, 0.1);
    transform: scale(1.02);
  }
  50% {
    background-color: rgba(66, 133, 244, 0.15);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}

/* Auth Content */
.auth-content {
  padding: 28px; /* Adjusted padding */
}

.auth-tab-content {
  display: none;
  opacity: 0;
  transform: translateY(8px); /* Slightly less translation */
  animation: tabContentFadeIn 0.35s ease-out forwards; /* Animation for tab content */
}

@keyframes tabContentFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-tab-content.active {
  display: block;
  /* Opacity and transform handled by animation when class is added */
}

/* Auth Forms */
.auth-form {
  margin-bottom: 28px; /* Adjusted margin */
}

.form-group {
  margin-bottom: 22px; /* Increased spacing */
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.form-group input {
  width: 100%;
  padding: 14px 18px; /* Increased padding */
  background: var(--input-bg); /* Ensure consistency */
  border: 1px solid var(--input-border);
  border-radius: 10px; /* Softer radius */
  font-size: 16px;
  color: var(--input-text);
  transition: all 0.25s ease;
  box-sizing: border-box;
}

/* Override browser default validation styles */
.form-group input:invalid {
  border-color: var(--input-border); /* Keep normal border */
  box-shadow: none; /* Remove browser default red shadow */
}

.form-group input:required {
  box-shadow: none; /* Remove any browser default styling */
}

/* Remove browser default required field styling */
.form-group input:required:invalid {
  border-color: var(--input-border);
  box-shadow: none;
}

.form-group input:focus {
  outline: none;
  border-color: var(--button-bg-start);
  box-shadow: 0 0 0 4px rgba(66, 133, 244, 0.15); /* Adjusted focus shadow */
}

.form-group input::placeholder {
  color: var(--input-placeholder);
}

.form-group input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.25); /* Adjusted error focus shadow */
}

.error-message {
  /* Field specific error messages */
  color: #f16666; /* Brighter red */
  font-size: 13px; /* Slightly larger */
  margin-top: 6px; /* Adjusted spacing */
  display: block;
  font-weight: 500;
}

/* Submit Button */
.auth-submit-btn {
  width: 100%;
  background: linear-gradient(
    135deg,
    var(--button-bg-start),
    var(--button-bg-end)
  );
  color: white;
  border: none;
  padding: 15px; /* Adjusted padding */
  border-radius: 10px; /* Softer radius */
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.25s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.auth-submit-btn:hover {
  transform: translateY(-2px); /* Lift effect */
  box-shadow: 0 6px 18px rgba(66, 133, 244, 0.45); /* Enhanced hover shadow */
}

.auth-submit-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(66, 133, 244, 0.3);
}

.auth-submit-btn .btn-text {
  transition: opacity 0.2s ease;
}
.auth-submit-btn .btn-loader {
  position: absolute;
  opacity: 0;
  transition: opacity 0.2s ease;
}
.auth-submit-btn.loading .btn-text {
  opacity: 0;
}
.auth-submit-btn.loading .btn-loader {
  opacity: 1;
}

.spinner {
  width: 20px; /* Larger spinner */
  height: 20px;
  border: 3px solid transparent; /* Thicker border */
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite; /* Faster spin */
}

/* Divider */
.auth-divider {
  position: relative;
  text-align: center;
  margin: 28px 0; /* Adjusted margin */
  border-top: 1px solid var(--border-color);
}

.auth-divider span {
  background: var(--input-bg); /* Match card background */
  padding: 0 12px; /* Adjusted padding */
  color: var(--text-secondary);
  font-size: 13px; /* Slightly larger */
  position: relative;
  top: -10px; /* Adjusted position */
  font-weight: 500;
}

/* Google Auth Button */
.google-auth-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: transparent; /* Transparent background */
  color: var(--text-primary); /* Use primary text color */
  border: 1px solid var(--input-border); /* Use input border color */
  padding: 14px; /* Adjusted padding */
  border-radius: 10px; /* Softer radius */
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.25s ease;
}

.google-auth-btn:hover {
  background: rgba(255, 255, 255, 0.05); /* Subtle hover */
  border-color: var(--button-bg-start); /* Highlight border on hover */
  color: var(--button-bg-start); /* Change text color on hover */
}
body.light-theme .google-auth-btn {
  background: #fff;
  color: #374151;
  border: 1px solid #d1d5db;
}
body.light-theme .google-auth-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151; /* Keep text color consistent for light theme google button */
}

.google-icon {
  width: 22px; /* Slightly larger icon */
  height: 22px;
}

/* General Auth Error/Success Alert */
.auth-error {
  /* This class is used for both error and success messages */
  border-radius: 10px; /* Softer radius */
  padding: 14px 18px; /* Adjusted padding */
  margin-top: 22px; /* Adjusted margin */
  display: none; /* Keep hidden by default */
  align-items: flex-start; /* Align items to top for multi-line messages */
  gap: 10px; /* Adjusted gap */
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s ease;
}

.auth-error .error-icon {
  /* Generic icon container */
  font-size: 18px;
  margin-top: 1px; /* Align icon better with text */
}

.auth-error .error-text {
  /* Generic text container */
  flex: 1;
}

/* Specific styling for error messages */
.auth-error:not(.success) {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}
.auth-error:not(.success) .error-icon {
  color: #ef4444;
}
.auth-error:not(.success) .error-text {
  color: #f16666; /* Brighter red for text */
}

/* Specific styling for success messages */
.auth-error.success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4caf50;
}
.auth-error.success .error-icon {
  color: #4caf50;
}
.auth-error.success .error-text {
  color: #66bb6a; /* Brighter green for text */
}

/* Authentication Required Message Styling */
.auth-required-message {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.1),
    rgba(255, 152, 0, 0.1)
  );
  border: 2px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 25px;
  margin: 15px 0;
  text-align: center;
  font-family: "Segoe UI", sans-serif;
}

.auth-required-message h3 {
  color: #ffc107;
  margin: 0 0 15px 0;
  font-size: 1.4em;
  text-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
}

.auth-required-message p {
  color: var(--text-primary);
  margin: 10px 0;
  line-height: 1.5;
  font-size: 1.1em;
}

.auth-required-btn {
  background: linear-gradient(135deg, #ffc107, #ff9800);
  color: #000;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.auth-required-btn:hover {
  background: linear-gradient(135deg, #ffca28, #ffa726);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.auth-required-btn:active {
  transform: translateY(-1px);
}

body.light-theme .auth-required-message {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(0, 0, 0, 0.1);
}

body.light-theme .auth-required-message h3 {
  color: #333;
  text-shadow: none;
}

body.light-theme .auth-required-message p {
  color: #555;
}

body.light-theme .auth-required-btn {
  background: linear-gradient(135deg, #ffd54f, #ffb300);
  color: #000;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

body.light-theme .auth-required-btn:hover {
  background: linear-gradient(135deg, #ffe082, #ffb300);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

body.light-theme .auth-required-btn:active {
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .menu-btn {
    display: flex;
  }

  .header-actions {
    gap: 8px;
  }

  .agent-mode-toggle-button,
  .theme-toggle-button {
    padding: 4px 8px;
    font-size: 1rem;
    margin-right: 8px;
  }

  .title-container h1 {
    font-size: 2.5rem;
  }

  .agent-mode-indicator {
    font-size: 0.7rem;
    padding: 4px 8px;
  }

  .input-area {
    flex-direction: column;
    gap: 15px;
  }

  .input-area select {
    align-self: flex-start;
    width: auto;
    min-width: 150px;
  }

  .example-prompts button {
    margin-right: 5px;
    margin-bottom: 8px;
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  header {
    padding: 20px 20px;
  }

  .logo {
    font-size: 1.5rem;
  }

  .agent-mode-toggle-button,
  .theme-toggle-button {
    padding: 3px 6px;
    font-size: 0.9rem;
    margin-right: 6px;
  }

  .title-container h1 {
    font-size: 2rem;
  }

  .agent-mode-indicator {
    font-size: 0.6rem;
    padding: 3px 6px;
  }

  .main-content {
    padding: 20px 20px;
  }

  textarea {
    height: 120px;
    font-size: 14px;
  }

  .example-prompts {
    padding: 15px;
  }

  .example-prompts button {
    font-size: 0.75rem;
    padding: 5px 10px;
  }
}

/* Agent Stream Interface - Simple terminal-like output */
.agent-stream {
  background: #0d1117;
  border: 1px solid #30363d;
  border-radius: 8px;
  overflow: hidden;
  font-family: "JetBrains Mono", "Monaco", "Consolas", monospace;
  margin-top: 20px;
}

.stream-header {
  background: #161b22;
  padding: 12px 16px;
  border-bottom: 1px solid #30363d;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.stream-title {
  color: #f0f6fc;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #238636;
  display: inline-block;
  animation: pulse-green 2s infinite;
}

.connection-indicator.disconnected {
  background: #da3633;
  animation: pulse-red 2s infinite;
}

.connection-indicator.connecting {
  background: #fb8500;
  animation: pulse-orange 1s infinite;
}

@keyframes pulse-green {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pulse-red {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pulse-orange {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.stream-status {
  background: #238636;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.stream-status.status-starting {
  background: #1f6feb;
}

.stream-status.status-processing {
  background: #fb8500;
}

.stream-status.status-thinking {
  background: #9a6dff;
}

.stream-status.status-executing {
  background: #da3633;
}

.stream-status.status-finalizing {
  background: #2ea043;
}

.stream-status.status-opening_vscode {
  background: #2ea043;
}

.stream-status.status-completed {
  background: #238636;
}

.stream-status.status-error {
  background: #da3633;
}

.stream-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  background: #0d1117;
}

.stream-line {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
  color: #e6edf3;
  font-size: 13px;
  line-height: 1.4;
}

.stream-line.current-line {
  color: #58a6ff;
}

.stream-line.success-line {
  color: #3fb950;
}

.stream-timestamp {
  color: #7d8590;
  font-size: 11px;
  min-width: 65px;
  font-family: monospace;
}

.stream-text {
  flex: 1;
  word-break: break-word;
}

.cursor {
  color: #58a6ff;
  animation: blink 1s infinite;
  font-weight: bold;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Scrollbar styling for stream content */
.stream-content::-webkit-scrollbar {
  width: 6px;
}

.stream-content::-webkit-scrollbar-track {
  background: #161b22;
}

.stream-content::-webkit-scrollbar-thumb {
  background: #30363d;
  border-radius: 3px;
}

.stream-content::-webkit-scrollbar-thumb:hover {
  background: #484f58;
}

.stream-status.status-tool_completed {
  background: #2ea043;
}

.stream-line.tool-line {
  border-left: 2px solid #58a6ff;
  padding-left: 10px;
  background: rgba(88, 166, 255, 0.05);
}

.stream-line.tool-line .stream-text {
  font-family: "JetBrains Mono", monospace;
  font-size: 12px;
}
