// Test script to verify agent mode functionality
import fetch from 'node-fetch';

async function testAgentMode() {
    console.log('🧪 Testing Agent Mode Integration...\n');

    try {
        const response = await fetch('https://ai-coder-agentic-suhailult777s-projects.vercel.app/api/agent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: 'Create a simple calculator HTML page with CSS styling'
            })
        });

        const responseText = await response.text();
        console.log('📊 Response Status:', response.status);
        console.log('📋 Response Text:', responseText.substring(0, 200) + '...');

        if (response.ok) {
            try {
                const data = JSON.parse(responseText);
                console.log('✅ Agent Mode Test PASSED!');
                console.log('Response:', JSON.stringify(data, null, 2));
            } catch (parseError) {
                console.log('⚠️ Response is not JSON:', responseText.substring(0, 100));
            }
        } else {
            console.log('❌ Agent Mode Test FAILED!');
            console.log('Status:', response.status);
            console.log('Response:', responseText.substring(0, 200));
        }
    } catch (error) {
        console.log('💥 Test Error:', error.message);
    }
}

// Run the test
testAgentMode();
